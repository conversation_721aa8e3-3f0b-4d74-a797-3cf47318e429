/**
 * GameConfig.ts
 * Global game configuration system for Genesis
 * Manages resolution settings, scaling factors, and virtual coordinate system
 */

export interface ResolutionConfig {
  // Virtual resolution - all game logic works in this coordinate system
  virtualWidth: number;
  virtualHeight: number;

  // Target resolution - what the game actually renders at
  targetWidth: number;
  targetHeight: number;

  // Calculated scaling factor (integer for pixel-perfect scaling)
  scaleFactor: number;
}

export interface GameConfigData {
  // Resolution settings
  resolution: ResolutionConfig;

  // Game settings
  physics: {
    gravity: { x: number; y: number };
    enableSleeping: boolean;
    debug: boolean;
  };

  // Input settings
  input: {
    gamepadEnabled: boolean;
  };

  // Debug settings
  debug: {
    showFPS: boolean;
    showBounds: boolean;
  };
}

class GameConfig {
  private static instance: GameConfig;
  private config: GameConfigData;

  // Supported target resolutions (all must be integer multiples of virtual resolution)
  private static readonly SUPPORTED_RESOLUTIONS: Array<{
    width: number;
    height: number;
    scale: number;
  }> = [
    { width: 640, height: 480, scale: 2 }, // 2x scale
    { width: 1280, height: 960, scale: 4 }, // 4x scale
    { width: 1920, height: 1440, scale: 6 }, // 6x scale
    { width: 2560, height: 1920, scale: 8 }, // 8x scale
  ];

  private constructor() {
    // Default configuration
    this.config = {
      resolution: {
        virtualWidth: 320,
        virtualHeight: 240,
        // targetWidth: 1280,
        // targetHeight: 960,
        // scaleFactor: 4,
        targetWidth: 640,
        targetHeight: 480,
        scaleFactor: 2,
        // targetWidth: 320,
        // targetHeight: 240,
        // scaleFactor: 1,
      },
      physics: {
        gravity: { x: 0, y: 0 },
        enableSleeping: false,
        debug: false,
      },
      input: {
        gamepadEnabled: true,
      },
      debug: {
        showFPS: false,
        showBounds: false,
      },
    };
  }

  public static getInstance(): GameConfig {
    if (!GameConfig.instance) {
      GameConfig.instance = new GameConfig();
    }
    return GameConfig.instance;
  }

  public getConfig(): GameConfigData {
    return { ...this.config };
  }

  public getResolution(): ResolutionConfig {
    return { ...this.config.resolution };
  }

  public setTargetResolution(width: number, height: number): boolean {
    // Find matching supported resolution
    const supportedRes = GameConfig.SUPPORTED_RESOLUTIONS.find(
      (res) => res.width === width && res.height === height
    );

    if (!supportedRes) {
      console.warn(`Unsupported resolution: ${width}x${height}`);
      return false;
    }

    // Validate that it's an integer multiple of virtual resolution
    const scaleX = width / this.config.resolution.virtualWidth;
    const scaleY = height / this.config.resolution.virtualHeight;

    if (scaleX !== scaleY || scaleX !== Math.floor(scaleX)) {
      console.warn(
        `Resolution ${width}x${height} is not a valid integer scale of virtual resolution`
      );
      return false;
    }

    this.config.resolution.targetWidth = width;
    this.config.resolution.targetHeight = height;
    this.config.resolution.scaleFactor = supportedRes.scale;

    console.log(
      `Resolution set to ${width}x${height} (${supportedRes.scale}x scale)`
    );
    return true;
  }

  public getSupportedResolutions(): Array<{
    width: number;
    height: number;
    scale: number;
  }> {
    return [...GameConfig.SUPPORTED_RESOLUTIONS];
  }

  public getVirtualWidth(): number {
    return this.config.resolution.virtualWidth;
  }

  public getVirtualHeight(): number {
    return this.config.resolution.virtualHeight;
  }

  public getTargetWidth(): number {
    return this.config.resolution.targetWidth;
  }

  public getTargetHeight(): number {
    return this.config.resolution.targetHeight;
  }

  public getScaleFactor(): number {
    return this.config.resolution.scaleFactor;
  }

  public updateConfig(updates: Partial<GameConfigData>): void {
    this.config = { ...this.config, ...updates };
  }
}

export { GameConfig };
