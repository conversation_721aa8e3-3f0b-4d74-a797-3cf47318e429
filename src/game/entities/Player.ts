import { InputSystem } from "../systems/InputSystem";
import { BulletManager } from "../systems/BulletManager";
import { BulletType } from "./Bullet";
import { GameConfig } from "../config/GameConfig";
import { ResolutionManager } from "../systems/ResolutionManager";

export enum MovementMode {
  Free = "free",
  Sideways = "sideways",
}

class Player extends Phaser.Physics.Matter.Sprite {
  // Movement properties (will be scaled based on resolution)
  private baseShipSpeed: number = 5; // Base speed in virtual coordinates
  private shipSpeed: number = 5; // Actual speed (scaled)
  private shipRotationSpeed: number = 3;

  // Constants
  private readonly SHIP_SCALE: number = 0.11;

  // World bounds (using virtual coordinates)
  private WORLD_WIDTH: number;
  private WORLD_HEIGHT: number;

  // Resolution management
  private gameConfig: GameConfig;
  private resolutionManager: ResolutionManager;

  // Input system
  private inputSystem: InputSystem;

  // Movement modes
  private movementMode: MovementMode = MovementMode.Sideways;

  // Sideways mode rotation properties
  private shipTargetRotation: number = 0;
  private shipRotationVelocity: number = 0;
  private readonly shipRotationAcceleration: number = 50; // How fast rotation changes
  private readonly shipRotationFriction: number = 0.9; // How much rotation slows down
  private readonly maxLeanAngle: number = 0.2; // Maximum lean angle in radians (~17 degrees)

  // Engine particle system
  private engineParticles: Array<{
    graphics: Phaser.GameObjects.Graphics;
    x: number;
    y: number;
    velocityX: number;
    velocityY: number;
    createdTime: number;
  }> = [];
  private readonly engineParticleMaxCount: number = 15;
  private readonly engineParticleLifetime: number = 200; // ms
  private readonly engineParticleSpeed: number = 4; // Reduced from 150 for Matter.js
  private readonly engineParticleSpread: number = 0.5; // radians
  private lastEngineParticleTime: number = 0;
  private readonly engineParticleInterval: number = 30; // ms
  private isThrusting: boolean = false;

  // Bullet system
  private bulletManager: BulletManager | null = null;
  private lastFired: number = 0;
  private readonly fireRate: number = 200; // milliseconds between shots
  private sceneStartTime: number = 0; // Track when scene started
  private readonly fireInputDelay: number = 300; // Prevent firing for first 300ms of scene

  constructor(
    scene: Phaser.Scene,
    x?: number,
    y?: number,
    texture?: string,
    frame?: number | string
  ) {
    super(scene.matter.world, x ?? 160, y ?? 200, texture || "player", frame);

    // Initialize resolution management
    this.gameConfig = GameConfig.getInstance();
    this.resolutionManager = ResolutionManager.getInstance();

    // Get world constants from virtual resolution
    const virtualRes = this.resolutionManager.getVirtualResolution();
    this.WORLD_WIDTH = virtualRes.width;
    this.WORLD_HEIGHT = virtualRes.height;

    // Get input system instance
    this.inputSystem = InputSystem.getInstance();

    // Scale movement speed based on resolution
    const scaleFactor = this.resolutionManager.getScaleFactor();
    this.shipSpeed = this.baseShipSpeed * scaleFactor;

    // Create ship visuals
    this.createShipVisuals();

    // Position ship (convert virtual coordinates to screen coordinates)
    const virtualPos = { x: this.WORLD_WIDTH / 2, y: this.WORLD_HEIGHT - 25 };
    const screenPos = this.resolutionManager.virtualToScreen(virtualPos);
    this.x = screenPos.x;
    this.y = screenPos.y;

    // Add to scene
    scene.add.existing(this);

    // Configure physics body
    this.setupPhysicsBody();
  }

  private setupPhysicsBody(): void {
    // Set collision bounds - triangle shape approximated with a rectangle
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const bodyWidth = 20 * scaleFactor;
    const bodyHeight = 48 * scaleFactor;
    this.setRectangle(bodyWidth, bodyHeight);

    // Configure physics properties to match original behavior
    this.setFrictionAir(0.5); // Use air friction instead of drag for responsive stopping
    this.setBounce(0); // No bounce
    this.setIgnoreGravity(true); // Ignore world gravity

    // Set collision category for player
    const playerCategory = 0x0001;
    const enemyBulletCategory = 0x0004;
    this.setCollisionCategory(playerCategory);
    this.setCollidesWith([enemyBulletCategory]);
  }

  private createShipVisuals(): void {
    // Set up the sprite with scaling based on resolution
    const scaleFactor = this.resolutionManager.getScaleFactor();
    this.setScale(this.SHIP_SCALE * scaleFactor);
  }

  public update(time: number, delta: number): void {
    // Set scene start time on first update
    if (this.sceneStartTime === 0) {
      this.sceneStartTime = time;
    }

    this.handleInput(time, delta);
    this.updateEngineParticles(time, delta);
  }

  private handleInput(time: number, delta: number): void {
    const deltaSeconds = delta / 1000;

    // Get movement vector from InputSystem (includes gamepad analog stick support)
    const movementVector = this.inputSystem.getRawMovementVector();
    const firePressed = this.inputSystem.isDown("action1");

    // Apply deadzone to prevent drift
    const deadzone = 0.1;
    const movementX =
      Math.abs(movementVector.x) > deadzone ? movementVector.x : 0;
    const movementY =
      Math.abs(movementVector.y) > deadzone ? movementVector.y : 0;

    if (this.movementMode === MovementMode.Free) {
      // Free mode - original behavior
      // Rotation based on analog input intensity
      if (movementX !== 0) {
        this.rotation += movementX * this.shipRotationSpeed * deltaSeconds;
      }

      // Store thrusting state for engine particles
      this.isThrusting = movementY < 0; // Forward thrust (up)

      // Apply velocity directly based on analog input intensity for immediate response
      if (movementY !== 0) {
        // movementY < 0 = forward, movementY > 0 = backward
        const velocityX = Math.sin(this.rotation) * this.shipSpeed * -movementY;
        const velocityY =
          -Math.cos(this.rotation) * this.shipSpeed * -movementY;

        // Clamp velocity to max speed (since Matter doesn't have setMaxVelocity)
        const speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
        if (speed > this.shipSpeed) {
          const scale = this.shipSpeed / speed;
          this.setVelocity(velocityX * scale, velocityY * scale);
        } else {
          this.setVelocity(velocityX, velocityY);
        }
      } else {
        this.setVelocity(0, 0);
      }
    } else {
      // Sideways mode - ship leans in strafe direction with inertia

      // Store thrusting state for engine particles (any movement)
      this.isThrusting = movementX !== 0 || movementY !== 0;

      // Calculate target rotation based on horizontal movement intensity
      this.shipTargetRotation = movementX * this.maxLeanAngle;

      // Apply rotation acceleration towards target
      const rotationDifference = this.shipTargetRotation - this.rotation;
      this.shipRotationVelocity +=
        rotationDifference * this.shipRotationAcceleration * deltaSeconds;

      // Apply rotation friction
      this.shipRotationVelocity *= this.shipRotationFriction;

      // Update ship rotation
      this.rotation += this.shipRotationVelocity * deltaSeconds;

      // Apply velocity directly based on analog input intensity for immediate response
      const velocityX = movementX * this.shipSpeed;
      const velocityY = movementY * this.shipSpeed;

      // Clamp velocity to max speed (since Matter doesn't have setMaxVelocity)
      const speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
      if (speed > this.shipSpeed) {
        const scale = this.shipSpeed / speed;
        this.setVelocity(velocityX * scale, velocityY * scale);
      } else {
        this.setVelocity(velocityX, velocityY);
      }
    }

    // Firing (with delay to prevent immediate firing when scene starts)
    const timeSinceSceneStart = time - this.sceneStartTime;
    if (
      firePressed &&
      time > this.lastFired + this.fireRate &&
      timeSinceSceneStart > this.fireInputDelay &&
      this.bulletManager
    ) {
      this.fireBullet();
      this.lastFired = time;
    }
  }

  // Getter methods for accessing ship properties
  public getShip(): Phaser.Physics.Matter.Sprite {
    return this;
  }

  public getPosition(): { x: number; y: number } {
    return { x: this.x, y: this.y };
  }

  public getRotation(): number {
    return this.rotation;
  }

  public setMovementMode(mode: MovementMode): void {
    this.movementMode = mode;

    // Reset rotation properties when switching to sideways mode
    if (mode === MovementMode.Sideways) {
      this.shipTargetRotation = 0;
      this.shipRotationVelocity = 0;
      // Don't immediately reset rotation - let it naturally return to upright
    }
  }

  public getMovementMode(): MovementMode {
    return this.movementMode;
  }

  public toggleMovementMode(): void {
    this.setMovementMode(
      this.movementMode === MovementMode.Free
        ? MovementMode.Sideways
        : MovementMode.Free
    );
  }

  private updateEngineParticles(time: number, delta: number): void {
    const deltaSeconds = delta / 1000;

    // Create new particles when thrusting
    if (
      this.isThrusting &&
      time > this.lastEngineParticleTime + this.engineParticleInterval
    ) {
      this.createEngineParticle(time);
      this.lastEngineParticleTime = time;
    }

    // Update existing particles
    const particlesToRemove: number[] = [];
    this.engineParticles.forEach((particle, index) => {
      const age = time - particle.createdTime;
      const lifeRatio = age / this.engineParticleLifetime;

      if (lifeRatio >= 1) {
        // Particle has expired
        particlesToRemove.push(index);
      } else {
        // Update particle position
        particle.x += particle.velocityX * deltaSeconds;
        particle.y += particle.velocityY * deltaSeconds;

        // Update alpha based on age (fade out over time)
        const alpha = 1 - lifeRatio;
        particle.graphics.alpha = alpha;
        particle.graphics.x = particle.x;
        particle.graphics.y = particle.y;
      }
    });

    // Remove expired particles
    particlesToRemove.reverse().forEach((index) => {
      const particle = this.engineParticles[index];
      particle.graphics.destroy();
      this.engineParticles.splice(index, 1);
    });
  }

  private createEngineParticle(time: number): void {
    // Limit the number of particles (now creating 2 at a time, so check against double the max)
    while (this.engineParticles.length >= this.engineParticleMaxCount) {
      const oldestParticle = this.engineParticles.shift();
      if (oldestParticle) {
        oldestParticle.graphics.destroy();
      }
    }

    // Calculate positions for both engines
    const backOffsetDistance = 80 * this.SHIP_SCALE; // Distance from ship center to back
    const sideOffsetDistance = 32 * this.SHIP_SCALE; // Distance from ship center to sides

    // Create particles for both engines
    for (let engineSide = -1; engineSide <= 1; engineSide += 2) {
      // -1 for left, 1 for right
      let backX: number, backY: number;

      if (this.movementMode === MovementMode.Sideways) {
        // In sideways mode, particles come from the bottom sides of the ship
        backX = this.x + engineSide * sideOffsetDistance;
        backY = this.y + backOffsetDistance;
      } else {
        // In free mode, particles come from behind the ship based on rotation
        const cos = Math.cos(this.rotation);
        const sin = Math.sin(this.rotation);

        // Calculate side offset perpendicular to ship direction
        const sideX = engineSide * sideOffsetDistance * cos;
        const sideY = engineSide * sideOffsetDistance * sin;

        // Calculate back offset opposite to ship direction
        backX = this.x - sin * backOffsetDistance + sideX;
        backY = this.y + cos * backOffsetDistance + sideY;
      }

      // Create particle graphics (small triangle)
      const particleGraphics = this.scene.add.graphics();

      // Random color between yellow, orange, and red
      const colors = [0xffff00, 0xff8800, 0xff4400, 0xff0000]; // Yellow to red
      const color = colors[Math.floor(Math.random() * colors.length)];

      particleGraphics.fillStyle(color);
      particleGraphics.beginPath();
      particleGraphics.moveTo(0, -3);
      particleGraphics.lineTo(-2, 2);
      particleGraphics.lineTo(2, 2);
      particleGraphics.closePath();
      particleGraphics.fillPath();

      // Calculate velocity based on movement mode
      let velocityX: number, velocityY: number;

      if (this.movementMode === MovementMode.Sideways) {
        // In sideways mode, particles always go downward with some spread
        const spreadAngle = (Math.random() - 0.5) * this.engineParticleSpread;
        velocityX = Math.sin(spreadAngle) * this.engineParticleSpeed;
        velocityY = Math.cos(spreadAngle) * this.engineParticleSpeed; // Positive Y = downward
      } else {
        // In free mode, particles go opposite to ship's facing direction
        const baseAngle = this.rotation + Math.PI; // Opposite direction
        const spreadAngle = (Math.random() - 0.5) * this.engineParticleSpread;
        const finalAngle = baseAngle + spreadAngle;

        velocityX = Math.sin(finalAngle) * this.engineParticleSpeed;
        velocityY = -Math.cos(finalAngle) * this.engineParticleSpeed;
      }

      // Store the particle
      this.engineParticles.push({
        graphics: particleGraphics,
        x: backX,
        y: backY,
        velocityX: velocityX,
        velocityY: velocityY,
        createdTime: time,
      });

      // Set initial position
      particleGraphics.x = backX;
      particleGraphics.y = backY;
    }
  }

  private fireBullet(): void {
    if (!this.bulletManager) return;

    // Calculate position at the tip of the ship
    const tipOffset = 15 * this.SHIP_SCALE; // Distance from center to ship tip
    const startX = this.x + tipOffset * Math.sin(this.rotation);
    const startY = this.y - tipOffset * Math.cos(this.rotation);

    // Fire the bullet straight up (0 rotation) regardless of ship facing direction
    this.bulletManager.fireBullet(
      startX,
      startY,
      0, // Always fire straight up
      BulletType.Player
    );
  }

  public setBulletManager(bulletManager: BulletManager): void {
    this.bulletManager = bulletManager;
  }
}

export { Player };
