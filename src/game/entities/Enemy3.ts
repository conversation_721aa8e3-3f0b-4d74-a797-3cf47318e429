import { Enemy, EnemyConfig } from "./Enemy";
import { BulletManager } from "../systems/BulletManager";
import { ResolutionManager } from "../systems/ResolutionManager";

/**
 * Enemy Type 3: Player Follower
 * - Follows the player with gradual turning behavior (current Enemy behavior)
 * - Faster than the other two enemy types
 * - Higher shooting rate
 * - Most aggressive and challenging enemy type
 */
class Enemy3 extends Enemy {
  constructor(
    scene: Phaser.Scene,
    x?: number,
    y?: number,
    config: EnemyConfig = {},
    bulletManager: BulletManager | null = null
  ) {
    // Set default configuration for Enemy3
    const scaleFactor = ResolutionManager.getInstance().getScaleFactor();
    const enemy3Config: EnemyConfig = {
      maxHealth: 2, // Slightly more health
      color: 0x4444ff, // Light blue
      size: 0.8,
      speed: 1.5 * scaleFactor, // Faster than other enemies
      turnSpeed: 0.35, // Slightly faster turning than base
      fireRate: 1200, // Faster shooting rate
      shootInFacingDirection: true,
      scoreValue: 150, // Higher score value
      ...config, // Allow overrides
    };

    // Call parent constructor with enemy3 texture
    super(scene, x, y, enemy3Config, bulletManager, "enemy3");
  }

  protected initializeMovement(): void {
    // Start with initial downward movement (same as base Enemy)
    this.setVelocity(0, this.speed);
    this.rotation = 0;
  }

  protected updateMovement(
    time: number,
    delta: number,
    playerPosition?: { x: number; y: number }
  ): void {
    // Use the same player-following logic as the base Enemy class
    // This is the current behavior that works well
    if (!playerPosition) return;

    const deltaSeconds = delta / 1000;

    // Direction to player for gradual turning behaviour
    const angleToPlayer = Math.atan2(
      playerPosition.y - this.y,
      playerPosition.x - this.x
    );

    // Current facing angle (based on current velocity)
    let currentAngle = Math.atan2(this.body!.velocity.y, this.body!.velocity.x);

    // Shortest angular distance to player
    let angleDifference = angleToPlayer - currentAngle;
    while (angleDifference > Math.PI) angleDifference -= 2 * Math.PI;
    while (angleDifference < -Math.PI) angleDifference += 2 * Math.PI;

    // Apply gradual turning
    const maxTurnThisFrame = this.turnSpeed * deltaSeconds;
    const turnAmount =
      Math.sign(angleDifference) *
      Math.min(Math.abs(angleDifference), maxTurnThisFrame);
    currentAngle += turnAmount;

    // Maintain speed, update velocity using physics
    const newVelX = Math.cos(currentAngle) * this.speed;
    const newVelY = Math.sin(currentAngle) * this.speed;
    this.setVelocity(newVelX, newVelY);

    // Update rotation to match movement
    this.rotation = Math.atan2(this.body!.velocity.x, -this.body!.velocity.y);
  }

  protected updateShooting(
    time: number,
    playerPosition?: { x: number; y: number }
  ): void {
    if (!playerPosition || !this.bulletManager) return;
    
    if (time > this.nextFireTime) {
      // Shoot in the direction the enemy is facing
      const direction = this.rotation;
      this.shoot(direction);
      // Faster, more consistent shooting rate
      this.nextFireTime = time + this.fireRate;
    }
  }
}

export { Enemy3 };
